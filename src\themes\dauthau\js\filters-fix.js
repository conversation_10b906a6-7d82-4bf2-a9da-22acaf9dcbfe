/**
 * File khắc phục lỗi cho trang filters
 * Xử lý các lỗi JavaScript và đảm bảo tính ổn định
 */

// Xử lý lỗi JavaScript chung
(function() {
    'use strict';
    
    // Ngăn chặn lỗi JavaScript làm crash trang
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
        console.error('Message:', e.message);
        console.error('Source:', e.filename, 'Line:', e.lineno, 'Column:', e.colno);
        
        // Ngăn lỗi lan truyền
        return true;
    });
    
    // Xử lý lỗi Promise
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
        e.preventDefault();
    });
    
    // Đảm bảo jQuery có sẵn
    function ensureJQuery(callback) {
        if (typeof jQuery !== 'undefined') {
            callback(jQuery);
        } else {
            setTimeout(function() {
                ensureJQuery(callback);
            }, 100);
        }
    }
    
    // Khởi tạo khi DOM ready
    ensureJQuery(function($) {
        $(document).ready(function() {
            
            // Kiểm tra và khởi tạo các biến cần thiết
            if (typeof window.fobj === 'undefined') {
                window.fobj = $("form.filters-form");
            }
            
            // Kiểm tra bootbox
            if (typeof window.bootbox === 'undefined') {
                window.bootbox = {
                    confirm: function(message, callback) {
                        var result = confirm(message.replace(/<[^>]*>/g, ''));
                        if (typeof callback === 'function') {
                            setTimeout(function() {
                                callback(result);
                            }, 10);
                        }
                    },
                    alert: function(message) {
                        if (typeof message === 'object' && message.message) {
                            alert(message.message.replace(/<[^>]*>/g, ''));
                            if (message.onHidden && typeof message.onHidden === 'function') {
                                setTimeout(message.onHidden, 10);
                            }
                        } else {
                            alert(message.replace(/<[^>]*>/g, ''));
                        }
                    }
                };
            }
            
            // Kiểm tra modalShow
            if (typeof window.modalShow === 'undefined') {
                window.modalShow = function(title, content) {
                    alert(content.replace(/<[^>]*>/g, ''));
                };
            }
            
            // Khắc phục lỗi cho các nút có thể gây lỗi
            $('a[href*="filters/UserId"]').each(function() {
                var $this = $(this);
                var href = $this.attr('href');
                
                // Nếu href có dạng filters/UserId-xxx thì sửa lại
                if (href && href.indexOf('filters/UserId') !== -1) {
                    console.warn('Phát hiện URL không hợp lệ:', href);
                    
                    // Thay thế bằng javascript:void(0) để tránh lỗi
                    $this.attr('href', 'javascript:void(0);');
                    
                    // Thêm click handler
                    $this.off('click').on('click', function(e) {
                        e.preventDefault();
                        alert('Chức năng này đang được bảo trì. Vui lòng thử lại sau.');
                        return false;
                    });
                }
            });
            
            // Khắc phục lỗi cho các onclick có thể gây lỗi
            $('[onclick*="filters/UserId"]').each(function() {
                var $this = $(this);
                console.warn('Phát hiện onclick không hợp lệ:', $this.attr('onclick'));
                
                // Xóa onclick cũ và thêm handler mới
                $this.removeAttr('onclick').off('click').on('click', function(e) {
                    e.preventDefault();
                    alert('Chức năng này đang được bảo trì. Vui lòng thử lại sau.');
                    return false;
                });
            });
            
            // Đảm bảo các hàm cần thiết tồn tại
            if (typeof window.delete_confirm === 'undefined') {
                window.delete_confirm = function(element) {
                    return confirm('Bạn có chắc chắn muốn xóa?');
                };
            }
            
            // Kiểm tra và sửa các biến template có thể gây lỗi
            var requiredVars = ['script_name', 'nv_name_variable', 'nv_module_name', 'nv_fc_variable'];
            requiredVars.forEach(function(varName) {
                if (typeof window[varName] === 'undefined') {
                    console.warn('Biến ' + varName + ' chưa được định nghĩa');
                    // Tạo giá trị mặc định
                    switch(varName) {
                        case 'script_name':
                            window[varName] = '/index.php';
                            break;
                        case 'nv_name_variable':
                            window[varName] = 'nv';
                            break;
                        case 'nv_module_name':
                            window[varName] = 'bidding';
                            break;
                        case 'nv_fc_variable':
                            window[varName] = 'op';
                            break;
                    }
                }
            });
            
            console.log('Filters fix script loaded successfully');
        });
    });
    
})();
